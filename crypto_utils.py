"""
比特币加密货币工具模块
实现BIP39助记词生成、私钥/公钥生成、HD钱包地址派生等核心功能
"""

import hashlib
import hmac
import struct
from mnemonic import Mnemonic
from hdwallet import HDWallet
from hdwallet.symbols import BTC
import bitcoin
import ecdsa
import base58
import binascii
from typing import List, Tuple, Dict
import secrets


class CryptoUtils:
    """加密货币工具类"""
    
    def __init__(self):
        self.mnemo = Mnemonic("english")
    
    def generate_mnemonic(self, word_count: int = 12) -> str:
        """
        生成BIP39助记词
        
        Args:
            word_count: 助记词数量，支持12或24
            
        Returns:
            助记词字符串
        """
        if word_count not in [12, 24]:
            raise ValueError("助记词数量只支持12或24个单词")
        
        # 生成熵
        entropy_bits = 128 if word_count == 12 else 256
        entropy = secrets.randbits(entropy_bits)
        entropy_bytes = entropy.to_bytes(entropy_bits // 8, 'big')
        
        # 生成助记词
        mnemonic = self.mnemo.to_mnemonic(entropy_bytes)
        return mnemonic
    
    def mnemonic_to_seed(self, mnemonic: str, passphrase: str = "") -> bytes:
        """
        将助记词转换为种子
        
        Args:
            mnemonic: 助记词
            passphrase: 可选的密码短语
            
        Returns:
            64字节的种子
        """
        return self.mnemo.to_seed(mnemonic, passphrase)
    
    def derive_master_key(self, seed: bytes) -> Tuple[bytes, bytes]:
        """
        从种子派生主私钥和链码
        
        Args:
            seed: 种子字节
            
        Returns:
            (主私钥, 链码)
        """
        hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
        master_private_key = hmac_result[:32]
        chain_code = hmac_result[32:]
        return master_private_key, chain_code
    
    def private_key_to_public_key(self, private_key: bytes, compressed: bool = True) -> bytes:
        """
        从私钥生成公钥
        
        Args:
            private_key: 32字节私钥
            compressed: 是否使用压缩公钥
            
        Returns:
            公钥字节
        """
        # 使用ecdsa库生成公钥
        sk = ecdsa.SigningKey.from_string(private_key, curve=ecdsa.SECP256k1)
        vk = sk.get_verifying_key()
        
        if compressed:
            # 压缩公钥格式
            point = vk.pubkey.point
            x = point.x()
            y = point.y()
            prefix = b'\x02' if y % 2 == 0 else b'\x03'
            return prefix + x.to_bytes(32, 'big')
        else:
            # 非压缩公钥格式
            return b'\x04' + vk.to_string()
    
    def public_key_to_address(self, public_key: bytes, address_type: str = "p2pkh") -> str:
        """
        从公钥生成比特币地址
        
        Args:
            public_key: 公钥字节
            address_type: 地址类型 ("p2pkh", "p2sh", "bech32")
            
        Returns:
            比特币地址字符串
        """
        if address_type == "p2pkh":
            # P2PKH地址 (1开头)
            sha256_hash = hashlib.sha256(public_key).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
            
            # 添加版本字节 (主网为0x00)
            versioned_hash = b'\x00' + ripemd160_hash
            
            # 计算校验和
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            
            # 生成最终地址
            address_bytes = versioned_hash + checksum
            return base58.b58encode(address_bytes).decode('utf-8')
        
        elif address_type == "p2sh":
            # P2SH地址 (3开头) - 简化实现
            sha256_hash = hashlib.sha256(public_key).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()
            
            # P2SH版本字节为0x05
            versioned_hash = b'\x05' + ripemd160_hash
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            address_bytes = versioned_hash + checksum
            return base58.b58encode(address_bytes).decode('utf-8')
        
        else:
            raise ValueError(f"不支持的地址类型: {address_type}")
    
    def derive_hd_addresses(self, mnemonic: str, derivation_path: str = "m/44'/0'/0'/0", 
                           count: int = 20, compressed: bool = True) -> List[Dict]:
        """
        从助记词派生HD钱包地址
        
        Args:
            mnemonic: 助记词
            derivation_path: 派生路径
            count: 生成地址数量
            compressed: 是否使用压缩公钥
            
        Returns:
            包含地址信息的字典列表
        """
        try:
            # 使用hdwallet库进行HD钱包派生
            hdwallet = HDWallet(symbol=BTC)
            hdwallet.from_mnemonic(mnemonic)
            
            addresses = []
            
            for i in range(count):
                # 构建完整的派生路径
                full_path = f"{derivation_path}/{i}"
                
                # 派生到指定路径
                hdwallet.from_path(full_path)
                
                # 获取私钥和公钥
                private_key_hex = hdwallet.private_key()
                private_key_bytes = bytes.fromhex(private_key_hex)
                
                # 生成公钥
                public_key = self.private_key_to_public_key(private_key_bytes, compressed)
                
                # 生成地址
                address = self.public_key_to_address(public_key, "p2pkh")
                
                addresses.append({
                    'index': i,
                    'path': full_path,
                    'private_key': private_key_hex,
                    'public_key': public_key.hex(),
                    'address': address
                })
                
                # 重置hdwallet以便下次派生
                hdwallet.from_mnemonic(mnemonic)
            
            return addresses
            
        except Exception as e:
            print(f"HD钱包派生错误: {e}")
            return []
    
    def validate_mnemonic(self, mnemonic: str) -> bool:
        """
        验证助记词是否有效
        
        Args:
            mnemonic: 助记词字符串
            
        Returns:
            是否有效
        """
        return self.mnemo.check(mnemonic)


# 测试函数
if __name__ == "__main__":
    crypto = CryptoUtils()
    
    # 测试生成助记词
    mnemonic = crypto.generate_mnemonic(12)
    print(f"生成的助记词: {mnemonic}")
    
    # 测试验证助记词
    is_valid = crypto.validate_mnemonic(mnemonic)
    print(f"助记词有效性: {is_valid}")
    
    # 测试派生地址
    addresses = crypto.derive_hd_addresses(mnemonic, count=5)
    for addr in addresses:
        print(f"地址 {addr['index']}: {addr['address']}")
        print(f"私钥: {addr['private_key']}")
        print("---")
