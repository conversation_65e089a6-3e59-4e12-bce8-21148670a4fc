"""
验证生成的地址数量和格式
"""

def verify_log_file():
    """验证log.txt文件中的地址数量"""
    try:
        with open("log.txt", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 统计每组数据
        groups = content.split("=== 第")
        print(f"总共生成了 {len(groups)-1} 组测试数据")
        
        for i in range(1, len(groups)):
            if i > 10:  # 只检查前10组
                break
                
            group = groups[i]
            print(f"\n=== 第 {i} 组数据分析 ===")
            
            # 统计接收地址
            receive_count = group.count("m/44'/0'/0'/0/")
            print(f"接收地址数量: {receive_count}")
            
            # 统计找零地址
            change_count = group.count("m/44'/0'/0'/1/")
            print(f"找零地址数量: {change_count}")
            
            # 统计总地址数
            total_addresses = group.count("地址: 1")
            print(f"总地址数量: {total_addresses}")
            
            # 验证路径格式
            if receive_count == 20 and change_count == 10:
                print("✅ 地址数量正确")
            else:
                print("❌ 地址数量不正确")
        
        # 验证地址格式
        print("\n=== 地址格式验证 ===")
        addresses = []
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith("地址: 1"):
                addr = line.split("地址: ")[1].strip()
                addresses.append(addr)
        
        print(f"总共提取到 {len(addresses)} 个地址")
        
        # 检查地址唯一性
        unique_addresses = set(addresses)
        print(f"唯一地址数量: {len(unique_addresses)}")
        
        if len(addresses) == len(unique_addresses):
            print("✅ 所有地址都是唯一的")
        else:
            print("❌ 存在重复地址")
        
        # 检查地址格式
        valid_addresses = 0
        for addr in addresses:
            if addr.startswith('1') and len(addr) >= 26 and len(addr) <= 35:
                valid_addresses += 1
        
        print(f"有效格式地址: {valid_addresses}/{len(addresses)}")
        
        if valid_addresses == len(addresses):
            print("✅ 所有地址格式都正确")
        else:
            print("❌ 存在格式错误的地址")
            
    except Exception as e:
        print(f"验证失败: {e}")

if __name__ == "__main__":
    verify_log_file()
