"""
系统托盘功能模块
实现程序最小化到托盘、托盘右键菜单、显示/隐藏程序功能
"""

import pystray
from PIL import Image, ImageDraw
import threading
import tkinter as tk
from typing import Callable


class SystemTray:
    """系统托盘类"""
    
    def __init__(self, root: tk.Tk, app_name: str = "比特币助记词碰撞器"):
        """
        初始化系统托盘
        
        Args:
            root: tkinter主窗口
            app_name: 应用程序名称
        """
        self.root = root
        self.app_name = app_name
        self.icon = None
        self.is_visible = True
        
        # 创建托盘图标
        self.create_icon()
        
        # 绑定窗口事件
        self.setup_window_events()
    
    def create_icon(self):
        """创建托盘图标"""
        # 创建一个简单的图标
        image = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(image)
        
        # 绘制一个简单的比特币符号
        # 外圆
        draw.ellipse([8, 8, 56, 56], fill='orange', outline='darkorange', width=2)
        
        # 绘制B字符
        draw.text((20, 18), "B", fill='white')
        
        # 创建托盘菜单
        menu = pystray.Menu(
            pystray.MenuItem("显示程序", self.show_window, default=True),
            pystray.MenuItem("隐藏程序", self.hide_window),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("关于", self.show_about),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("退出程序", self.quit_application)
        )
        
        # 创建托盘图标对象
        self.icon = pystray.Icon(
            name=self.app_name,
            icon=image,
            title=self.app_name,
            menu=menu
        )
    
    def setup_window_events(self):
        """设置窗口事件"""
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 绑定窗口状态变化事件
        self.root.bind("<Unmap>", self.on_window_unmap)
        self.root.bind("<Map>", self.on_window_map)
    
    def on_window_close(self):
        """窗口关闭事件处理"""
        # 最小化到托盘而不是直接关闭
        self.hide_window()
    
    def on_window_unmap(self, event):
        """窗口取消映射事件（最小化）"""
        if event.widget == self.root:
            # 检查是否是最小化操作
            if self.root.state() == 'iconic':
                self.hide_window()
    
    def on_window_map(self, event):
        """窗口映射事件（显示）"""
        if event.widget == self.root:
            self.is_visible = True
    
    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.root.after(0, self._show_window_impl)
    
    def _show_window_impl(self):
        """显示窗口的实际实现"""
        self.root.deiconify()  # 取消最小化
        self.root.lift()       # 提升窗口
        self.root.focus_force()  # 强制获取焦点
        self.is_visible = True
    
    def hide_window(self, icon=None, item=None):
        """隐藏主窗口"""
        self.root.after(0, self._hide_window_impl)
    
    def _hide_window_impl(self):
        """隐藏窗口的实际实现"""
        self.root.withdraw()  # 隐藏窗口
        self.is_visible = False
    
    def show_about(self, icon=None, item=None):
        """显示关于对话框"""
        self.root.after(0, self._show_about_impl)
    
    def _show_about_impl(self):
        """显示关于对话框的实际实现"""
        import tkinter.messagebox as messagebox
        
        about_text = f"""
{self.app_name} v1.0

一个基于Python的比特币助记词本地碰撞器

功能特性：
• 支持BIP39标准的12/24位助记词
• 支持压缩/非压缩公钥选项
• 可配置HD钱包派生路径
• 多线程高效碰撞
• 系统托盘最小化
• 实时统计和日志记录

注意：本程序仅用于教育和研究目的
请勿用于非法用途！

开发者：AI Assistant
"""
        
        # 确保主窗口可见
        if not self.is_visible:
            self.show_window()
        
        messagebox.showinfo("关于", about_text)
    
    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        # 停止托盘图标
        if self.icon:
            self.icon.stop()
        
        # 关闭主窗口
        self.root.after(0, self.root.quit)
    
    def start_tray(self):
        """启动系统托盘（在单独线程中运行）"""
        if self.icon:
            # 在单独线程中运行托盘
            tray_thread = threading.Thread(target=self.icon.run, daemon=True)
            tray_thread.start()
    
    def stop_tray(self):
        """停止系统托盘"""
        if self.icon:
            self.icon.stop()
    
    def update_tooltip(self, text: str):
        """更新托盘图标提示文本"""
        if self.icon:
            self.icon.title = text
    
    def show_notification(self, title: str, message: str):
        """显示系统通知"""
        if self.icon:
            try:
                self.icon.notify(message, title)
            except Exception as e:
                print(f"显示通知失败: {e}")


class TrayManager:
    """托盘管理器"""
    
    def __init__(self, root: tk.Tk):
        """
        初始化托盘管理器
        
        Args:
            root: tkinter主窗口
        """
        self.root = root
        self.tray = None
        self.notification_callback = None
    
    def setup_tray(self, app_name: str = "比特币助记词碰撞器"):
        """设置系统托盘"""
        try:
            self.tray = SystemTray(self.root, app_name)
            self.tray.start_tray()
            return True
        except Exception as e:
            print(f"设置系统托盘失败: {e}")
            return False
    
    def set_notification_callback(self, callback: Callable):
        """设置通知回调函数"""
        self.notification_callback = callback
    
    def show_collision_notification(self, address: str, mnemonic: str):
        """显示碰撞成功通知"""
        if self.tray:
            title = "🎉 碰撞成功！"
            message = f"发现匹配地址: {address[:20]}..."
            self.tray.show_notification(title, message)
    
    def update_status(self, status: str, stats: dict = None):
        """更新托盘状态"""
        if self.tray:
            if stats:
                tooltip = f"{status} - 生成: {stats.get('generation_count', 0)}, 成功: {stats.get('success_count', 0)}"
            else:
                tooltip = status
            self.tray.update_tooltip(tooltip)
    
    def cleanup(self):
        """清理资源"""
        if self.tray:
            self.tray.stop_tray()


# 测试代码
if __name__ == "__main__":
    import time
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("托盘测试")
    root.geometry("400x300")
    
    # 添加一些测试控件
    label = tk.Label(root, text="这是一个托盘功能测试窗口")
    label.pack(pady=20)
    
    button_hide = tk.Button(root, text="隐藏到托盘", 
                           command=lambda: tray_manager.tray.hide_window())
    button_hide.pack(pady=10)
    
    button_notify = tk.Button(root, text="测试通知", 
                             command=lambda: tray_manager.show_collision_notification(
                                 "1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa", 
                                 "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"))
    button_notify.pack(pady=10)
    
    # 设置托盘管理器
    tray_manager = TrayManager(root)
    if tray_manager.setup_tray("托盘测试程序"):
        print("托盘设置成功")
    else:
        print("托盘设置失败")
    
    # 运行主循环
    try:
        root.mainloop()
    finally:
        tray_manager.cleanup()
