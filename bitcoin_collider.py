"""
比特币助记词本地碰撞器 - 完整版
整合所有功能到一个文件中
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
import queue
import os
import sys
from datetime import datetime
from typing import Set, Dict, List, Tuple, Callable
import hashlib
import hmac
import struct
import secrets
import binascii

# 导入依赖包
try:
    from mnemonic import Mnemonic
    from hdwallet import HDWallet
    from hdwallet.symbols import BTC
    import bitcoin
    import ecdsa
    import base58
    import pystray
    from PIL import Image, ImageDraw
except ImportError as e:
    print(f"❌ 依赖包缺失: {e}")
    print("请运行: pip install -r requirements.txt")
    input("按回车键退出...")
    sys.exit(1)


# ==================== 加密货币工具模块 ====================

class CryptoUtils:
    """加密货币工具类"""

    def __init__(self):
        self.mnemo = Mnemonic("english")

    def generate_mnemonic(self, word_count: int = 12) -> str:
        """
        生成BIP39助记词

        Args:
            word_count: 助记词数量，支持12或24

        Returns:
            助记词字符串
        """
        if word_count not in [12, 24]:
            raise ValueError("助记词数量只支持12或24个单词")

        # 生成熵
        entropy_bits = 128 if word_count == 12 else 256
        entropy = secrets.randbits(entropy_bits)
        entropy_bytes = entropy.to_bytes(entropy_bits // 8, 'big')

        # 生成助记词
        mnemonic = self.mnemo.to_mnemonic(entropy_bytes)
        return mnemonic

    def mnemonic_to_seed(self, mnemonic: str, passphrase: str = "") -> bytes:
        """
        将助记词转换为种子

        Args:
            mnemonic: 助记词
            passphrase: 可选的密码短语

        Returns:
            64字节的种子
        """
        return self.mnemo.to_seed(mnemonic, passphrase)

    def derive_master_key(self, seed: bytes) -> Tuple[bytes, bytes]:
        """
        从种子派生主私钥和链码

        Args:
            seed: 种子字节

        Returns:
            (主私钥, 链码)
        """
        hmac_result = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
        master_private_key = hmac_result[:32]
        chain_code = hmac_result[32:]
        return master_private_key, chain_code

    def private_key_to_public_key(self, private_key: bytes, compressed: bool = True) -> bytes:
        """
        从私钥生成公钥

        Args:
            private_key: 32字节私钥
            compressed: 是否使用压缩公钥

        Returns:
            公钥字节
        """
        # 使用ecdsa库生成公钥
        sk = ecdsa.SigningKey.from_string(private_key, curve=ecdsa.SECP256k1)
        vk = sk.get_verifying_key()

        if compressed:
            # 压缩公钥格式
            point = vk.pubkey.point
            x = point.x()
            y = point.y()
            prefix = b'\x02' if y % 2 == 0 else b'\x03'
            return prefix + x.to_bytes(32, 'big')
        else:
            # 非压缩公钥格式
            return b'\x04' + vk.to_string()

    def public_key_to_address(self, public_key: bytes, address_type: str = "p2pkh") -> str:
        """
        从公钥生成比特币地址

        Args:
            public_key: 公钥字节
            address_type: 地址类型 ("p2pkh", "p2sh", "bech32")

        Returns:
            比特币地址字符串
        """
        if address_type == "p2pkh":
            # P2PKH地址 (1开头)
            sha256_hash = hashlib.sha256(public_key).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()

            # 添加版本字节 (主网为0x00)
            versioned_hash = b'\x00' + ripemd160_hash

            # 计算校验和
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]

            # 生成最终地址
            address_bytes = versioned_hash + checksum
            return base58.b58encode(address_bytes).decode('utf-8')

        elif address_type == "p2sh":
            # P2SH地址 (3开头) - 简化实现
            sha256_hash = hashlib.sha256(public_key).digest()
            ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()

            # P2SH版本字节为0x05
            versioned_hash = b'\x05' + ripemd160_hash
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            address_bytes = versioned_hash + checksum
            return base58.b58encode(address_bytes).decode('utf-8')

        else:
            raise ValueError(f"不支持的地址类型: {address_type}")

    def derive_hd_addresses(self, mnemonic: str, derivation_path: str = "m/44'/0'/0'/0",
                           count: int = 20, compressed: bool = True) -> List[Dict]:
        """
        从助记词派生HD钱包地址

        Args:
            mnemonic: 助记词
            derivation_path: 派生路径
            count: 生成地址数量
            compressed: 是否使用压缩公钥

        Returns:
            包含地址信息的字典列表
        """
        try:
            # 使用hdwallet库进行HD钱包派生
            hdwallet = HDWallet(symbol=BTC)
            hdwallet.from_mnemonic(mnemonic)

            addresses = []

            for i in range(count):
                # 构建完整的派生路径
                full_path = f"{derivation_path}/{i}"

                # 派生到指定路径
                hdwallet.from_path(full_path)

                # 获取私钥和公钥
                private_key_hex = hdwallet.private_key()
                private_key_bytes = bytes.fromhex(private_key_hex)

                # 生成公钥
                public_key = self.private_key_to_public_key(private_key_bytes, compressed)

                # 生成地址
                address = self.public_key_to_address(public_key, "p2pkh")

                addresses.append({
                    'index': i,
                    'path': full_path,
                    'private_key': private_key_hex,
                    'public_key': public_key.hex(),
                    'address': address
                })

                # 重置hdwallet以便下次派生
                hdwallet.from_mnemonic(mnemonic)

            return addresses

        except Exception as e:
            print(f"HD钱包派生错误: {e}")
            return []

    def validate_mnemonic(self, mnemonic: str) -> bool:
        """
        验证助记词是否有效

        Args:
            mnemonic: 助记词字符串

        Returns:
            是否有效
        """
        return self.mnemo.check(mnemonic)


# ==================== 碰撞引擎模块 ====================

class CollisionConfig:
    """碰撞配置类"""

    def __init__(self):
        self.mnemonic_type = 12  # 12 or 24
        self.compression_type = "compressed"  # "compressed" or "uncompressed"
        self.derivation_path = "m/44'/0'/0'/0"
        self.thread_count = 4
        self.address_count_per_mnemonic = 20  # 每个助记词生成的地址数量

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'mnemonic_type': self.mnemonic_type,
            'compression_type': self.compression_type,
            'derivation_path': self.derivation_path,
            'thread_count': self.thread_count,
            'address_count_per_mnemonic': self.address_count_per_mnemonic
        }

    def from_dict(self, config_dict: Dict):
        """从字典加载配置"""
        self.mnemonic_type = config_dict.get('mnemonic_type', 12)
        self.compression_type = config_dict.get('compression_type', 'compressed')
        self.derivation_path = config_dict.get('derivation_path', "m/44'/0'/0'/0")
        self.thread_count = config_dict.get('thread_count', 4)
        self.address_count_per_mnemonic = config_dict.get('address_count_per_mnemonic', 20)

    def validate(self) -> List[str]:
        """验证配置"""
        errors = []

        if self.mnemonic_type not in [12, 24]:
            errors.append("助记词类型必须是12或24")

        if self.compression_type not in ["compressed", "uncompressed"]:
            errors.append("压缩类型必须是compressed或uncompressed")

        if not self.derivation_path.startswith("m/"):
            errors.append("派生路径格式不正确")

        if self.thread_count < 1 or self.thread_count > 32:
            errors.append("线程数必须在1-32之间")

        if self.address_count_per_mnemonic < 1 or self.address_count_per_mnemonic > 100:
            errors.append("每个助记词的地址数量必须在1-100之间")

        return errors


class CollisionEngine:
    """碰撞引擎类"""

    def __init__(self, target_addresses: Set[str], config: Dict,
                 log_callback: Callable, stats_callback: Callable):
        """
        初始化碰撞引擎

        Args:
            target_addresses: 目标地址集合
            config: 配置字典
            log_callback: 日志回调函数
            stats_callback: 统计回调函数
        """
        self.target_addresses = target_addresses
        self.config = config
        self.log_callback = log_callback
        self.stats_callback = stats_callback

        self.crypto_utils = CryptoUtils()
        self.is_running = False
        self.threads = []
        self.result_queue = queue.Queue()

        # 统计变量（线程安全）
        self.stats_lock = threading.Lock()
        self.generation_count = 0
        self.collision_count = 0
        self.success_count = 0
        self.current_mnemonic = ""

        # 结果处理线程
        self.result_thread = None

    def start(self):
        """启动碰撞引擎"""
        if self.is_running:
            return

        self.is_running = True
        self.generation_count = 0
        self.collision_count = 0
        self.success_count = 0

        # 启动结果处理线程
        self.result_thread = threading.Thread(target=self._process_results, daemon=True)
        self.result_thread.start()

        # 启动碰撞工作线程
        thread_count = int(self.config.get('thread_count', 4))
        for i in range(thread_count):
            thread = threading.Thread(target=self._collision_worker,
                                    args=(i,), daemon=True)
            thread.start()
            self.threads.append(thread)

        self.log_callback(f"碰撞引擎已启动，使用 {thread_count} 个线程", "info")

    def stop(self):
        """停止碰撞引擎"""
        self.is_running = False
        self.log_callback("正在停止碰撞引擎...", "warning")

        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=1)

        self.threads.clear()
        self.log_callback("碰撞引擎已停止", "info")

    def _collision_worker(self, worker_id: int):
        """碰撞工作线程"""
        self.log_callback(f"工作线程 {worker_id} 已启动", "info")

        while self.is_running:
            try:
                # 生成助记词
                word_count = int(self.config.get('mnemonic_type', 12))
                mnemonic = self.crypto_utils.generate_mnemonic(word_count)

                # 更新当前助记词显示
                with self.stats_lock:
                    self.current_mnemonic = mnemonic
                    self.generation_count += 1

                # 派生地址
                compressed = self.config.get('compression_type', 'compressed') == 'compressed'
                derivation_path = self.config.get('derivation_path', "m/44'/0'/0'/0")

                addresses = self.crypto_utils.derive_hd_addresses(
                    mnemonic, derivation_path, count=20, compressed=compressed
                )

                # 检查碰撞
                for addr_info in addresses:
                    address = addr_info['address']

                    if address in self.target_addresses:
                        # 发现碰撞！
                        with self.stats_lock:
                            self.collision_count += 1
                            self.success_count += 1

                        # 将结果放入队列
                        result = {
                            'mnemonic': mnemonic,
                            'address': address,
                            'private_key': addr_info['private_key'],
                            'path': addr_info['path'],
                            'worker_id': worker_id
                        }
                        self.result_queue.put(result)

                # 更新统计（每100次生成更新一次回调）
                if self.generation_count % 100 == 0:
                    self._update_stats_callback()

                # 短暂休息以避免CPU过载
                time.sleep(0.001)

            except Exception as e:
                self.log_callback(f"工作线程 {worker_id} 发生错误: {e}", "error")
                time.sleep(1)

        self.log_callback(f"工作线程 {worker_id} 已停止", "info")

    def _process_results(self):
        """处理碰撞结果"""
        while self.is_running or not self.result_queue.empty():
            try:
                result = self.result_queue.get(timeout=1)
                self._handle_collision_success(result)
            except queue.Empty:
                continue

    def _handle_collision_success(self, result: Dict):
        """处理碰撞成功"""
        mnemonic = result['mnemonic']
        address = result['address']
        private_key = result['private_key']
        path = result['path']
        worker_id = result['worker_id']

        # 记录到日志
        success_msg = f"🎉 碰撞成功！工作线程 {worker_id}"
        self.log_callback(success_msg, "success")
        self.log_callback(f"助记词: {mnemonic}", "success")
        self.log_callback(f"地址: {address}", "success")
        self.log_callback(f"私钥: {private_key}", "success")
        self.log_callback(f"路径: {path}", "success")
        self.log_callback("-" * 50, "success")

        # 保存到文件
        self._save_result_to_file(result)

        # 弹窗提醒
        try:
            messagebox.showinfo("碰撞成功！",
                              f"发现匹配地址！\n地址: {address}\n助记词: {mnemonic}")
        except:
            pass

    def _save_result_to_file(self, result: Dict):
        """保存结果到文件"""
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

            with open("log.txt", "a", encoding="utf-8") as f:
                f.write(f"\n=== 碰撞成功记录 ===\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"助记词: {result['mnemonic']}\n")
                f.write(f"匹配地址: {result['address']}\n")
                f.write(f"对应私钥: {result['private_key']}\n")
                f.write(f"派生路径: {result['path']}\n")
                f.write(f"工作线程: {result['worker_id']}\n")
                f.write("-" * 50 + "\n")

            self.log_callback("结果已保存到 log.txt", "info")

        except Exception as e:
            self.log_callback(f"保存结果到文件失败: {e}", "error")

    def _update_stats_callback(self):
        """更新统计回调"""
        if self.stats_callback:
            with self.stats_lock:
                stats = {
                    'generation_count': self.generation_count,
                    'collision_count': self.collision_count,
                    'success_count': self.success_count,
                    'current_mnemonic': self.current_mnemonic
                }
            self.stats_callback(stats)

    def get_stats(self) -> Dict:
        """获取当前统计信息"""
        with self.stats_lock:
            return {
                'generation_count': self.generation_count,
                'collision_count': self.collision_count,
                'success_count': self.success_count,
                'current_mnemonic': self.current_mnemonic,
                'is_running': self.is_running
            }


# ==================== 系统托盘模块 ====================

class SystemTray:
    """系统托盘类"""

    def __init__(self, root: tk.Tk, app_name: str = "比特币助记词碰撞器"):
        """
        初始化系统托盘

        Args:
            root: tkinter主窗口
            app_name: 应用程序名称
        """
        self.root = root
        self.app_name = app_name
        self.icon = None
        self.is_visible = True

        # 创建托盘图标
        self.create_icon()

        # 绑定窗口事件
        self.setup_window_events()

    def create_icon(self):
        """创建托盘图标"""
        # 创建一个简单的图标
        image = Image.new('RGB', (64, 64), color='white')
        draw = ImageDraw.Draw(image)

        # 绘制一个简单的比特币符号
        # 外圆
        draw.ellipse([8, 8, 56, 56], fill='orange', outline='darkorange', width=2)

        # 绘制B字符
        draw.text((20, 18), "B", fill='white')

        # 创建托盘菜单
        menu = pystray.Menu(
            pystray.MenuItem("显示程序", self.show_window, default=True),
            pystray.MenuItem("隐藏程序", self.hide_window),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("关于", self.show_about),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("退出程序", self.quit_application)
        )

        # 创建托盘图标对象
        self.icon = pystray.Icon(
            name=self.app_name,
            icon=image,
            title=self.app_name,
            menu=menu
        )

    def setup_window_events(self):
        """设置窗口事件"""
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

        # 绑定窗口状态变化事件
        self.root.bind("<Unmap>", self.on_window_unmap)
        self.root.bind("<Map>", self.on_window_map)

    def on_window_close(self):
        """窗口关闭事件处理"""
        # 最小化到托盘而不是直接关闭
        self.hide_window()

    def on_window_unmap(self, event):
        """窗口取消映射事件（最小化）"""
        if event.widget == self.root:
            # 检查是否是最小化操作
            if self.root.state() == 'iconic':
                self.hide_window()

    def on_window_map(self, event):
        """窗口映射事件（显示）"""
        if event.widget == self.root:
            self.is_visible = True

    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        self.root.after(0, self._show_window_impl)

    def _show_window_impl(self):
        """显示窗口的实际实现"""
        self.root.deiconify()  # 取消最小化
        self.root.lift()       # 提升窗口
        self.root.focus_force()  # 强制获取焦点
        self.is_visible = True

    def hide_window(self, icon=None, item=None):
        """隐藏主窗口"""
        self.root.after(0, self._hide_window_impl)

    def _hide_window_impl(self):
        """隐藏窗口的实际实现"""
        self.root.withdraw()  # 隐藏窗口
        self.is_visible = False

    def show_about(self, icon=None, item=None):
        """显示关于对话框"""
        self.root.after(0, self._show_about_impl)

    def _show_about_impl(self):
        """显示关于对话框的实际实现"""
        about_text = f"""
{self.app_name} v1.0

一个基于Python的比特币助记词本地碰撞器

功能特性：
• 支持BIP39标准的12/24位助记词
• 支持压缩/非压缩公钥选项
• 可配置HD钱包派生路径
• 多线程高效碰撞
• 系统托盘最小化
• 实时统计和日志记录

注意：本程序仅用于教育和研究目的
请勿用于非法用途！

开发者：AI Assistant
"""

        # 确保主窗口可见
        if not self.is_visible:
            self.show_window()

        messagebox.showinfo("关于", about_text)

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        # 停止托盘图标
        if self.icon:
            self.icon.stop()

        # 关闭主窗口
        self.root.after(0, self.root.quit)

    def start_tray(self):
        """启动系统托盘（在单独线程中运行）"""
        if self.icon:
            # 在单独线程中运行托盘
            tray_thread = threading.Thread(target=self.icon.run, daemon=True)
            tray_thread.start()

    def stop_tray(self):
        """停止系统托盘"""
        if self.icon:
            self.icon.stop()

    def update_tooltip(self, text: str):
        """更新托盘图标提示文本"""
        if self.icon:
            self.icon.title = text

    def show_notification(self, title: str, message: str):
        """显示系统通知"""
        if self.icon:
            try:
                self.icon.notify(message, title)
            except Exception as e:
                print(f"显示通知失败: {e}")


class TrayManager:
    """托盘管理器"""

    def __init__(self, root: tk.Tk):
        """
        初始化托盘管理器

        Args:
            root: tkinter主窗口
        """
        self.root = root
        self.tray = None
        self.notification_callback = None

    def setup_tray(self, app_name: str = "比特币助记词碰撞器"):
        """设置系统托盘"""
        try:
            self.tray = SystemTray(self.root, app_name)
            self.tray.start_tray()
            return True
        except Exception as e:
            print(f"设置系统托盘失败: {e}")
            return False

    def set_notification_callback(self, callback: Callable):
        """设置通知回调函数"""
        self.notification_callback = callback

    def show_collision_notification(self, address: str, mnemonic: str):
        """显示碰撞成功通知"""
        if self.tray:
            title = "🎉 碰撞成功！"
            message = f"发现匹配地址: {address[:20]}..."
            self.tray.show_notification(title, message)

    def update_status(self, status: str, stats: dict = None):
        """更新托盘状态"""
        if self.tray:
            if stats:
                tooltip = f"{status} - 生成: {stats.get('generation_count', 0)}, 成功: {stats.get('success_count', 0)}"
            else:
                tooltip = status
            self.tray.update_tooltip(tooltip)

    def cleanup(self):
        """清理资源"""
        if self.tray:
            self.tray.stop_tray()


# ==================== 主GUI应用程序 ====================

class BitcoinColliderApp:
    """比特币助记词碰撞器主应用类"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_tray()
        self.setup_collision_engine()

        # 应用状态
        self.target_addresses: Set[str] = set()
        self.collision_engine = None
        self.is_running = False
        self.start_time = None

        # 统计数据
        self.stats = {
            'generation_count': 0,
            'collision_count': 0,
            'success_count': 0,
            'current_mnemonic': '等待开始...'
        }

    def setup_window(self):
        """设置主窗口"""
        self.root.title("比特币助记词本地碰撞器 v1.0")
        self.root.geometry("700x700")
        self.root.minsize(600, 500)

        # 设置窗口居中
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_variables(self):
        """设置tkinter变量"""
        # 配置变量
        self.mnemonic_type = tk.StringVar(value="12")
        self.compression_type = tk.StringVar(value="compressed")
        self.derivation_path = tk.StringVar(value="m/44'/0'/0'/0")
        self.thread_count = tk.StringVar(value="4")
        self.target_file_path = tk.StringVar()

        # 统计显示变量
        self.runtime_var = tk.StringVar(value="00:00:00")
        self.generation_var = tk.StringVar(value="0")
        self.collision_var = tk.StringVar(value="0")
        self.success_var = tk.StringVar(value="0")
        self.current_mnemonic_var = tk.StringVar(value="等待开始...")

        # 日志队列
        self.log_queue = queue.Queue()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 创建各个区域
        self.create_config_area(main_frame)
        self.create_operation_area(main_frame)
        self.create_statistics_area(main_frame)
        self.create_log_area(main_frame)

        # 启动日志处理
        self.start_log_processor()

    def create_config_area(self, parent):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(parent, text="配置区", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        row = 0

        # 目标地址文件导入
        ttk.Label(config_frame, text="目标地址文件:").grid(row=row, column=0, sticky=tk.W, pady=2)
        file_frame = ttk.Frame(config_frame)
        file_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        file_frame.columnconfigure(0, weight=1)

        self.file_entry = ttk.Entry(file_frame, textvariable=self.target_file_path, state="readonly")
        self.file_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_target_file).grid(row=0, column=1)
        row += 1

        # 助记词类型
        ttk.Label(config_frame, text="助记词类型:").grid(row=row, column=0, sticky=tk.W, pady=2)
        mnemonic_frame = ttk.Frame(config_frame)
        mnemonic_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Radiobutton(mnemonic_frame, text="12个单词", variable=self.mnemonic_type, value="12").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(mnemonic_frame, text="24个单词", variable=self.mnemonic_type, value="24").grid(row=0, column=1)
        row += 1

        # 公钥压缩选项
        ttk.Label(config_frame, text="公钥压缩:").grid(row=row, column=0, sticky=tk.W, pady=2)
        compression_frame = ttk.Frame(config_frame)
        compression_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Radiobutton(compression_frame, text="压缩公钥", variable=self.compression_type, value="compressed").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(compression_frame, text="非压缩公钥", variable=self.compression_type, value="uncompressed").grid(row=0, column=1)
        row += 1

        # HD钱包派生路径
        ttk.Label(config_frame, text="HD钱包路径:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(config_frame, textvariable=self.derivation_path, width=30).grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        row += 1

        # 线程数设置
        ttk.Label(config_frame, text="线程数:").grid(row=row, column=0, sticky=tk.W, pady=2)
        thread_frame = ttk.Frame(config_frame)
        thread_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Spinbox(thread_frame, from_=1, to=16, textvariable=self.thread_count, width=10).grid(row=0, column=0)
        ttk.Label(thread_frame, text="(建议设置为CPU核心数)").grid(row=0, column=1, padx=(10, 0))

    def create_operation_area(self, parent):
        """创建操作区域"""
        operation_frame = ttk.LabelFrame(parent, text="操作区", padding="10")
        operation_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        button_frame = ttk.Frame(operation_frame)
        button_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(button_frame, text="开始碰撞", command=self.start_collision)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_collision, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)

    def create_statistics_area(self, parent):
        """创建统计区域"""
        stats_frame = ttk.LabelFrame(parent, text="运行统计", padding="10")
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(3, weight=1)

        # 统计标签
        ttk.Label(stats_frame, text="运行时间:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.runtime_var, foreground="blue").grid(row=0, column=1, sticky=tk.W, padx=(10, 20), pady=2)
        ttk.Label(stats_frame, text="生成数量:").grid(row=0, column=2, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.generation_var, foreground="green").grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(stats_frame, text="碰撞数量:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.collision_var, foreground="orange").grid(row=1, column=1, sticky=tk.W, padx=(10, 20), pady=2)
        ttk.Label(stats_frame, text="成功数量:").grid(row=1, column=2, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.success_var, foreground="red").grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        # 当前助记词
        ttk.Label(stats_frame, text="当前助记词:").grid(row=2, column=0, sticky=tk.W, pady=2)
        current_frame = ttk.Frame(stats_frame)
        current_frame.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        current_frame.columnconfigure(0, weight=1)
        ttk.Label(current_frame, textvariable=self.current_mnemonic_var, foreground="purple", font=("TkDefaultFont", 9)).grid(row=0, column=0, sticky=tk.W)

    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="日志区", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, state="disabled")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置文本标签
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green", background="lightgreen")
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("warning", foreground="orange")

    def setup_tray(self):
        """设置系统托盘"""
        try:
            self.tray_manager = TrayManager(self.root)
            if self.tray_manager.setup_tray():
                self.log_message("系统托盘设置成功", "info")
            else:
                self.log_message("系统托盘设置失败", "warning")
        except Exception as e:
            self.log_message(f"系统托盘初始化失败: {e}", "warning")
            self.tray_manager = None

    def setup_collision_engine(self):
        """设置碰撞引擎"""
        self.collision_engine = None

    def browse_target_file(self):
        """浏览目标地址文件"""
        filename = filedialog.askopenfilename(
            title="选择目标地址文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.target_file_path.set(filename)
            self.load_target_addresses(filename)

    def load_target_addresses(self, filename):
        """加载目标地址"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                addresses = [line.strip() for line in f if line.strip()]

            self.target_addresses = set(addresses)
            self.log_message(f"成功加载 {len(self.target_addresses)} 个目标地址", "info")

        except Exception as e:
            self.log_message(f"加载目标地址文件失败: {e}", "error")
            messagebox.showerror("错误", f"加载目标地址文件失败: {e}")

    def start_collision(self):
        """开始碰撞"""
        # 验证配置
        if not self.target_addresses:
            messagebox.showwarning("警告", "请先导入目标地址文件")
            return

        # 创建配置
        config = CollisionConfig()
        config.mnemonic_type = int(self.mnemonic_type.get())
        config.compression_type = self.compression_type.get()
        config.derivation_path = self.derivation_path.get()
        config.thread_count = int(self.thread_count.get())

        # 验证配置
        errors = config.validate()
        if errors:
            messagebox.showerror("配置错误", "\n".join(errors))
            return

        # 更新UI状态
        self.is_running = True
        self.start_time = time.time()
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # 创建并启动碰撞引擎
        self.collision_engine = CollisionEngine(
            self.target_addresses,
            config.to_dict(),
            self.log_message,
            self.update_stats_callback
        )

        self.collision_engine.start()
        self.log_message("碰撞引擎已启动", "info")

        # 启动统计更新线程
        self.stats_thread = threading.Thread(target=self.update_statistics_loop, daemon=True)
        self.stats_thread.start()

        # 更新托盘状态
        if self.tray_manager and self.tray_manager.tray:
            self.tray_manager.update_status("正在运行碰撞...")

    def stop_collision(self):
        """停止碰撞"""
        self.is_running = False

        if self.collision_engine:
            self.collision_engine.stop()
            self.collision_engine = None

        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.log_message("碰撞已停止", "warning")

        # 更新托盘状态
        if self.tray_manager and self.tray_manager.tray:
            self.tray_manager.update_status("已停止")

    def clear_results(self):
        """清空结果"""
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="disabled")

        # 重置统计
        self.stats = {
            'generation_count': 0,
            'collision_count': 0,
            'success_count': 0,
            'current_mnemonic': '等待开始...'
        }
        self.update_stats_display()

    def update_statistics_loop(self):
        """统计更新循环"""
        while self.is_running:
            if self.collision_engine:
                engine_stats = self.collision_engine.get_stats()
                self.stats.update(engine_stats)

            self.root.after(0, self.update_stats_display)
            time.sleep(1)

    def update_stats_display(self):
        """更新统计显示"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)
            self.runtime_var.set(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

        self.generation_var.set(str(self.stats['generation_count']))
        self.collision_var.set(str(self.stats['collision_count']))
        self.success_var.set(str(self.stats['success_count']))
        self.current_mnemonic_var.set(self.stats['current_mnemonic'])

    def update_stats_callback(self, stats):
        """统计回调函数"""
        self.stats.update(stats)

    def log_message(self, message, level="info"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_queue.put((f"[{timestamp}] {message}", level))

    def start_log_processor(self):
        """启动日志处理器"""
        def process_logs():
            while True:
                try:
                    message, level = self.log_queue.get(timeout=1)
                    self.root.after(0, self._add_log_message, message, level)
                except queue.Empty:
                    continue

        log_thread = threading.Thread(target=process_logs, daemon=True)
        log_thread.start()

    def _add_log_message(self, message, level):
        """添加日志消息到文本框"""
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, message + "\n", level)
        self.log_text.see(tk.END)
        self.log_text.config(state="disabled")

    def generate_test_data(self):
        """生成前10组测试数据写入log.txt"""
        try:
            crypto = CryptoUtils()

            with open("log.txt", "w", encoding="utf-8") as f:
                f.write("=== 比特币助记词碰撞器测试数据 ===\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")

                for i in range(10):
                    f.write(f"=== 第 {i+1} 组测试数据 ===\n")

                    # 生成12位助记词
                    mnemonic = crypto.generate_mnemonic(12)
                    f.write(f"助记词: {mnemonic}\n")

                    # 验证助记词
                    is_valid = crypto.validate_mnemonic(mnemonic)
                    f.write(f"助记词有效性: {is_valid}\n")

                    # 生成压缩公钥地址
                    addresses_compressed = crypto.derive_hd_addresses(
                        mnemonic, "m/44'/0'/0'/0", count=5, compressed=True
                    )

                    f.write("压缩公钥地址:\n")
                    for addr in addresses_compressed:
                        f.write(f"  路径: {addr['path']}\n")
                        f.write(f"  地址: {addr['address']}\n")
                        f.write(f"  私钥: {addr['private_key']}\n")
                        f.write(f"  公钥: {addr['public_key']}\n")
                        f.write("  ---\n")

                    # 生成非压缩公钥地址
                    addresses_uncompressed = crypto.derive_hd_addresses(
                        mnemonic, "m/44'/0'/0'/0", count=3, compressed=False
                    )

                    f.write("非压缩公钥地址:\n")
                    for addr in addresses_uncompressed:
                        f.write(f"  路径: {addr['path']}\n")
                        f.write(f"  地址: {addr['address']}\n")
                        f.write(f"  私钥: {addr['private_key']}\n")
                        f.write(f"  公钥: {addr['public_key']}\n")
                        f.write("  ---\n")

                    f.write("\n" + "=" * 60 + "\n\n")

                f.write("测试数据生成完成！\n")
                f.write("请检查以上数据的正确性。\n")

            self.log_message("已生成10组测试数据到log.txt文件", "info")
            self.log_message("请检查log.txt文件中的助记词、私钥、地址等信息", "info")

        except Exception as e:
            self.log_message(f"生成测试数据失败: {e}", "error")

    def run(self):
        """运行应用程序"""
        self.log_message("比特币助记词碰撞器已启动", "info")
        self.log_message("正在生成测试数据...", "info")

        # 生成测试数据
        self.generate_test_data()

        self.log_message("请导入目标地址文件并配置参数后开始碰撞", "info")

        try:
            self.root.mainloop()
        finally:
            # 清理资源
            if self.collision_engine:
                self.collision_engine.stop()
            if self.tray_manager:
                self.tray_manager.cleanup()


# ==================== 主函数 ====================

def main():
    """主函数"""
    print("正在启动比特币助记词碰撞器...")

    try:
        # 检查依赖
        print("检查依赖包...")
        print("✓ tkinter")
        print("✓ mnemonic")
        print("✓ bitcoin")
        print("✓ pystray")
        print("✓ Pillow")
        print("✓ hdwallet")
        print("✓ ecdsa")
        print("✓ base58")

        print("所有依赖包检查完成！")
        print("-" * 40)

        # 启动主程序
        print("启动主程序...")
        app = BitcoinColliderApp()
        app.run()

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("\n详细错误信息:")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()
