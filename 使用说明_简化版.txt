比特币助记词本地碰撞器 - 使用说明

=== 程序简介 ===
本程序是一个完整的比特币助记词碰撞器，所有功能都整合在一个Python文件中。
程序能够随机生成BIP39助记词，并与用户导入的目标地址进行比较。

=== 快速开始 ===
1. 确保已安装Python 3.7+
2. 安装依赖：pip install -r requirements.txt
3. 运行程序：python bitcoin_collider.py
4. 导入目标地址文件（txt格式，每行一个地址）
5. 配置参数并点击"开始碰撞"

=== 主要功能 ===
✓ 支持12/24位BIP39助记词生成
✓ 支持压缩/非压缩公钥
✓ 可配置HD钱包派生路径
✓ 多线程高效处理
✓ 系统托盘最小化
✓ 实时统计显示
✓ 碰撞成功自动提醒
✓ 结果自动保存到log.txt

=== 界面说明 ===
- 配置区：设置目标地址文件、助记词类型、公钥压缩、HD路径、线程数
- 操作区：开始碰撞、停止、清空结果
- 统计区：显示运行时间、生成数量、碰撞数量、成功数量
- 日志区：显示程序运行信息和碰撞结果

=== 系统托盘 ===
- 点击窗口关闭按钮程序会最小化到托盘
- 右键托盘图标可以显示/隐藏程序或退出
- 托盘图标会显示当前运行状态

=== 碰撞成功处理 ===
当发现匹配地址时：
1. 弹窗提醒用户
2. 在日志区显示详细信息
3. 自动保存到log.txt文件，包含：
   - 时间戳
   - 助记词
   - 匹配的地址
   - 对应的私钥
   - HD钱包派生路径

=== 注意事项 ===
⚠️ 本程序仅用于教育和研究目的
⚠️ 请勿用于非法用途
⚠️ 比特币地址碰撞在实际中几乎不可能成功
⚠️ 程序运行需要大量计算资源

=== 性能建议 ===
- 线程数建议设置为CPU核心数
- 目标地址越多，理论碰撞概率越高
- 建议使用多核CPU和充足内存

=== 故障排除 ===
如果程序无法启动：
1. 检查Python版本（建议3.7+）
2. 确认依赖包已正确安装
3. 查看控制台错误信息

如果托盘功能异常：
- 某些系统可能不支持托盘功能
- 程序仍可正常使用，只是无法最小化到托盘

=== 文件说明 ===
- bitcoin_collider.py：主程序文件（包含所有功能）
- requirements.txt：依赖包列表
- sample_addresses.txt：示例目标地址文件
- log.txt：碰撞成功记录（程序自动生成）

=== 技术特性 ===
- 基于BIP39和BIP44标准
- 使用ECDSA椭圆曲线算法
- SHA256和RIPEMD160哈希算法
- 多线程并发处理
- 线程安全的数据管理

联系支持：查看程序日志区的详细信息
