"""
简化的GUI测试程序
用于测试基本功能是否正常
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from crypto_utils import CryptoUtils
    print("crypto_utils 导入成功")
except Exception as e:
    print(f"crypto_utils 导入失败: {e}")

try:
    from collision_engine import CollisionEngine, CollisionConfig
    print("collision_engine 导入成功")
except Exception as e:
    print(f"collision_engine 导入失败: {e}")

try:
    from system_tray import TrayManager
    print("system_tray 导入成功")
except Exception as e:
    print(f"system_tray 导入失败: {e}")


class SimpleTestGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("比特币助记词碰撞器测试")
        self.root.geometry("600x400")
        
        self.setup_ui()
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="比特币助记词碰撞器", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 测试按钮
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(test_frame, text="测试助记词生成", 
                  command=self.test_mnemonic).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(test_frame, text="测试地址生成", 
                  command=self.test_address).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(test_frame, text="关于程序", 
                  command=self.show_about).pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        self.result_text = tk.Text(result_frame, height=15, width=70)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def test_mnemonic(self):
        """测试助记词生成"""
        try:
            crypto = CryptoUtils()
            
            # 生成12位助记词
            mnemonic12 = crypto.generate_mnemonic(12)
            self.add_result(f"12位助记词: {mnemonic12}")
            
            # 验证助记词
            is_valid = crypto.validate_mnemonic(mnemonic12)
            self.add_result(f"助记词有效性: {is_valid}")
            
            # 生成24位助记词
            mnemonic24 = crypto.generate_mnemonic(24)
            self.add_result(f"24位助记词: {mnemonic24}")
            
            self.add_result("助记词生成测试完成！\n" + "="*50)
            
        except Exception as e:
            self.add_result(f"助记词生成测试失败: {e}")
    
    def test_address(self):
        """测试地址生成"""
        try:
            crypto = CryptoUtils()
            
            # 生成测试助记词
            mnemonic = crypto.generate_mnemonic(12)
            self.add_result(f"测试助记词: {mnemonic}")
            
            # 生成地址
            addresses = crypto.derive_hd_addresses(mnemonic, count=5)
            
            self.add_result("生成的地址:")
            for i, addr in enumerate(addresses):
                self.add_result(f"  {i+1}. {addr['address']}")
                self.add_result(f"     私钥: {addr['private_key']}")
                self.add_result(f"     路径: {addr['path']}")
            
            self.add_result("地址生成测试完成！\n" + "="*50)
            
        except Exception as e:
            self.add_result(f"地址生成测试失败: {e}")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """比特币助记词本地碰撞器 v1.0

功能特性：
• 支持BIP39标准的12/24位助记词生成
• 支持压缩/非压缩公钥选项
• 可配置HD钱包派生路径
• 多线程高效碰撞
• 系统托盘最小化
• 实时统计和日志记录

注意：本程序仅用于教育和研究目的，请勿用于非法用途！"""
        
        messagebox.showinfo("关于", about_text)
    
    def add_result(self, text):
        """添加结果到文本框"""
        self.result_text.insert(tk.END, text + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run(self):
        """运行GUI"""
        self.add_result("比特币助记词碰撞器测试程序已启动")
        self.add_result("点击上方按钮进行功能测试")
        self.add_result("="*50)
        
        self.root.mainloop()


if __name__ == "__main__":
    print("启动测试GUI...")
    app = SimpleTestGUI()
    app.run()
