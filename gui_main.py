"""
比特币助记词碰撞器GUI主界面
使用tkinter创建主窗口，实现配置区、操作区、统计区、日志区的基本布局
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import os
import sys
from typing import Set, List
import queue


class BitcoinColliderGUI:
    """比特币助记词碰撞器GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_threading()
        
        # 碰撞相关变量
        self.target_addresses: Set[str] = set()
        self.is_running = False
        self.start_time = None
        self.collision_count = 0
        self.generation_count = 0
        self.success_count = 0
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("比特币助记词本地碰撞器 v1.0")
        self.root.geometry("700x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def setup_variables(self):
        """设置tkinter变量"""
        # 配置变量
        self.mnemonic_type = tk.StringVar(value="12")
        self.compression_type = tk.StringVar(value="compressed")
        self.derivation_path = tk.StringVar(value="m/44'/0'/0'/0")
        self.thread_count = tk.StringVar(value="4")
        self.target_file_path = tk.StringVar()
        
        # 统计变量
        self.runtime_var = tk.StringVar(value="00:00:00")
        self.generation_var = tk.StringVar(value="0")
        self.collision_var = tk.StringVar(value="0")
        self.success_var = tk.StringVar(value="0")
        self.current_mnemonic_var = tk.StringVar(value="等待开始...")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 创建各个区域
        self.create_config_area(main_frame)
        self.create_operation_area(main_frame)
        self.create_statistics_area(main_frame)
        self.create_log_area(main_frame)
    
    def create_config_area(self, parent):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(parent, text="配置区", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # 目标地址文件导入
        ttk.Label(config_frame, text="目标地址文件:").grid(row=row, column=0, sticky=tk.W, pady=2)
        file_frame = ttk.Frame(config_frame)
        file_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        file_frame.columnconfigure(0, weight=1)
        
        self.file_entry = ttk.Entry(file_frame, textvariable=self.target_file_path, state="readonly")
        self.file_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(file_frame, text="浏览", command=self.browse_target_file).grid(row=0, column=1)
        row += 1
        
        # 助记词类型
        ttk.Label(config_frame, text="助记词类型:").grid(row=row, column=0, sticky=tk.W, pady=2)
        mnemonic_frame = ttk.Frame(config_frame)
        mnemonic_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Radiobutton(mnemonic_frame, text="12个单词", variable=self.mnemonic_type, 
                       value="12").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(mnemonic_frame, text="24个单词", variable=self.mnemonic_type, 
                       value="24").grid(row=0, column=1)
        row += 1
        
        # 公钥压缩选项
        ttk.Label(config_frame, text="公钥压缩:").grid(row=row, column=0, sticky=tk.W, pady=2)
        compression_frame = ttk.Frame(config_frame)
        compression_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Radiobutton(compression_frame, text="压缩公钥", variable=self.compression_type, 
                       value="compressed").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(compression_frame, text="非压缩公钥", variable=self.compression_type, 
                       value="uncompressed").grid(row=0, column=1)
        row += 1
        
        # HD钱包派生路径
        ttk.Label(config_frame, text="HD钱包路径:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(config_frame, textvariable=self.derivation_path, width=30).grid(
            row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        row += 1
        
        # 线程数设置
        ttk.Label(config_frame, text="线程数:").grid(row=row, column=0, sticky=tk.W, pady=2)
        thread_frame = ttk.Frame(config_frame)
        thread_frame.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Spinbox(thread_frame, from_=1, to=16, textvariable=self.thread_count, 
                   width=10).grid(row=0, column=0)
        ttk.Label(thread_frame, text="(建议设置为CPU核心数)").grid(row=0, column=1, padx=(10, 0))
    
    def create_operation_area(self, parent):
        """创建操作区域"""
        operation_frame = ttk.LabelFrame(parent, text="操作区", padding="10")
        operation_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 操作按钮
        button_frame = ttk.Frame(operation_frame)
        button_frame.pack(fill=tk.X)
        
        self.start_button = ttk.Button(button_frame, text="开始碰撞", command=self.start_collision)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_collision, 
                                     state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)
    
    def create_statistics_area(self, parent):
        """创建统计区域"""
        stats_frame = ttk.LabelFrame(parent, text="运行统计", padding="10")
        stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(3, weight=1)
        
        # 第一行统计
        ttk.Label(stats_frame, text="运行时间:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.runtime_var, foreground="blue").grid(
            row=0, column=1, sticky=tk.W, padx=(10, 20), pady=2)
        
        ttk.Label(stats_frame, text="生成数量:").grid(row=0, column=2, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.generation_var, foreground="green").grid(
            row=0, column=3, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 第二行统计
        ttk.Label(stats_frame, text="碰撞数量:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.collision_var, foreground="orange").grid(
            row=1, column=1, sticky=tk.W, padx=(10, 20), pady=2)
        
        ttk.Label(stats_frame, text="成功数量:").grid(row=1, column=2, sticky=tk.W, pady=2)
        ttk.Label(stats_frame, textvariable=self.success_var, foreground="red").grid(
            row=1, column=3, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 当前助记词
        ttk.Label(stats_frame, text="当前助记词:").grid(row=2, column=0, sticky=tk.W, pady=2)
        current_frame = ttk.Frame(stats_frame)
        current_frame.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        current_frame.columnconfigure(0, weight=1)
        
        ttk.Label(current_frame, textvariable=self.current_mnemonic_var, foreground="purple",
                 font=("TkDefaultFont", 9)).grid(row=0, column=0, sticky=tk.W)
    
    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="日志区", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建滚动文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state="disabled")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置文本标签
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green", background="lightgreen")
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("warning", foreground="orange")
    
    def setup_threading(self):
        """设置线程相关"""
        self.log_queue = queue.Queue()
        self.collision_threads = []
        
        # 启动日志处理线程
        self.log_thread = threading.Thread(target=self.process_log_queue, daemon=True)
        self.log_thread.start()
    
    def browse_target_file(self):
        """浏览目标地址文件"""
        filename = filedialog.askopenfilename(
            title="选择目标地址文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.target_file_path.set(filename)
            self.load_target_addresses(filename)
    
    def load_target_addresses(self, filename):
        """加载目标地址"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                addresses = [line.strip() for line in f if line.strip()]
            
            self.target_addresses = set(addresses)
            self.log_message(f"成功加载 {len(self.target_addresses)} 个目标地址", "info")
            
        except Exception as e:
            self.log_message(f"加载目标地址文件失败: {e}", "error")
            messagebox.showerror("错误", f"加载目标地址文件失败: {e}")
    
    def start_collision(self):
        """开始碰撞"""
        # 验证配置
        if not self.target_addresses:
            messagebox.showwarning("警告", "请先导入目标地址文件")
            return
        
        # 更新UI状态
        self.is_running = True
        self.start_time = time.time()
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        # 重置统计
        self.collision_count = 0
        self.generation_count = 0
        self.success_count = 0
        
        self.log_message("开始碰撞...", "info")
        
        # 启动统计更新线程
        self.stats_thread = threading.Thread(target=self.update_statistics, daemon=True)
        self.stats_thread.start()
        
        # TODO: 启动碰撞线程（在下一个模块中实现）
    
    def stop_collision(self):
        """停止碰撞"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.log_message("碰撞已停止", "warning")
    
    def clear_results(self):
        """清空结果"""
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="disabled")
        
        # 重置统计
        self.collision_count = 0
        self.generation_count = 0
        self.success_count = 0
        self.current_mnemonic_var.set("等待开始...")
    
    def update_statistics(self):
        """更新统计信息"""
        while self.is_running:
            if self.start_time:
                # 计算运行时间
                elapsed = time.time() - self.start_time
                hours = int(elapsed // 3600)
                minutes = int((elapsed % 3600) // 60)
                seconds = int(elapsed % 60)
                self.runtime_var.set(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
                
                # 更新其他统计
                self.generation_var.set(str(self.generation_count))
                self.collision_var.set(str(self.collision_count))
                self.success_var.set(str(self.success_count))
            
            time.sleep(1)
    
    def log_message(self, message, level="info"):
        """添加日志消息到队列"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_queue.put((f"[{timestamp}] {message}", level))
    
    def process_log_queue(self):
        """处理日志队列"""
        while True:
            try:
                message, level = self.log_queue.get(timeout=1)
                self.root.after(0, self._add_log_message, message, level)
            except queue.Empty:
                continue
    
    def _add_log_message(self, message, level):
        """添加日志消息到文本框"""
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, message + "\n", level)
        self.log_text.see(tk.END)
        self.log_text.config(state="disabled")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = BitcoinColliderGUI()
    app.run()
