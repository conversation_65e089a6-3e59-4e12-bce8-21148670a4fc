"""
多线程碰撞引擎
实现高效的多线程碰撞算法，包括线程管理、内存优化、地址比较逻辑
"""

import threading
import time
import queue
from typing import Set, Dict, List, Callable
from crypto_utils import CryptoUtils
import os


class CollisionEngine:
    """碰撞引擎类"""
    
    def __init__(self, target_addresses: Set[str], config: Dict, 
                 log_callback: Callable, stats_callback: Callable):
        """
        初始化碰撞引擎
        
        Args:
            target_addresses: 目标地址集合
            config: 配置字典
            log_callback: 日志回调函数
            stats_callback: 统计回调函数
        """
        self.target_addresses = target_addresses
        self.config = config
        self.log_callback = log_callback
        self.stats_callback = stats_callback
        
        self.crypto_utils = CryptoUtils()
        self.is_running = False
        self.threads = []
        self.result_queue = queue.Queue()
        
        # 统计变量（线程安全）
        self.stats_lock = threading.Lock()
        self.generation_count = 0
        self.collision_count = 0
        self.success_count = 0
        self.current_mnemonic = ""
        
        # 结果处理线程
        self.result_thread = None
    
    def start(self):
        """启动碰撞引擎"""
        if self.is_running:
            return
        
        self.is_running = True
        self.generation_count = 0
        self.collision_count = 0
        self.success_count = 0
        
        # 启动结果处理线程
        self.result_thread = threading.Thread(target=self._process_results, daemon=True)
        self.result_thread.start()
        
        # 启动碰撞工作线程
        thread_count = int(self.config.get('thread_count', 4))
        for i in range(thread_count):
            thread = threading.Thread(target=self._collision_worker, 
                                    args=(i,), daemon=True)
            thread.start()
            self.threads.append(thread)
        
        self.log_callback(f"碰撞引擎已启动，使用 {thread_count} 个线程", "info")
    
    def stop(self):
        """停止碰撞引擎"""
        self.is_running = False
        self.log_callback("正在停止碰撞引擎...", "warning")
        
        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=1)
        
        self.threads.clear()
        self.log_callback("碰撞引擎已停止", "info")
    
    def _collision_worker(self, worker_id: int):
        """碰撞工作线程"""
        self.log_callback(f"工作线程 {worker_id} 已启动", "info")
        
        while self.is_running:
            try:
                # 生成助记词
                word_count = int(self.config.get('mnemonic_type', 12))
                mnemonic = self.crypto_utils.generate_mnemonic(word_count)
                
                # 更新当前助记词显示
                with self.stats_lock:
                    self.current_mnemonic = mnemonic
                    self.generation_count += 1
                
                # 派生地址
                compressed = self.config.get('compression_type', 'compressed') == 'compressed'
                derivation_path = self.config.get('derivation_path', "m/44'/0'/0'/0")
                
                addresses = self.crypto_utils.derive_hd_addresses(
                    mnemonic, derivation_path, count=20, compressed=compressed
                )
                
                # 检查碰撞
                for addr_info in addresses:
                    address = addr_info['address']
                    
                    if address in self.target_addresses:
                        # 发现碰撞！
                        with self.stats_lock:
                            self.collision_count += 1
                            self.success_count += 1
                        
                        # 将结果放入队列
                        result = {
                            'mnemonic': mnemonic,
                            'address': address,
                            'private_key': addr_info['private_key'],
                            'path': addr_info['path'],
                            'worker_id': worker_id
                        }
                        self.result_queue.put(result)
                
                # 更新统计（每100次生成更新一次回调）
                if self.generation_count % 100 == 0:
                    self._update_stats_callback()
                
                # 短暂休息以避免CPU过载
                time.sleep(0.001)
                
            except Exception as e:
                self.log_callback(f"工作线程 {worker_id} 发生错误: {e}", "error")
                time.sleep(1)
        
        self.log_callback(f"工作线程 {worker_id} 已停止", "info")
    
    def _process_results(self):
        """处理碰撞结果"""
        while self.is_running or not self.result_queue.empty():
            try:
                result = self.result_queue.get(timeout=1)
                self._handle_collision_success(result)
            except queue.Empty:
                continue
    
    def _handle_collision_success(self, result: Dict):
        """处理碰撞成功"""
        mnemonic = result['mnemonic']
        address = result['address']
        private_key = result['private_key']
        path = result['path']
        worker_id = result['worker_id']
        
        # 记录到日志
        success_msg = f"🎉 碰撞成功！工作线程 {worker_id}"
        self.log_callback(success_msg, "success")
        self.log_callback(f"助记词: {mnemonic}", "success")
        self.log_callback(f"地址: {address}", "success")
        self.log_callback(f"私钥: {private_key}", "success")
        self.log_callback(f"路径: {path}", "success")
        self.log_callback("-" * 50, "success")
        
        # 保存到文件
        self._save_result_to_file(result)
        
        # 弹窗提醒
        try:
            import tkinter.messagebox as messagebox
            messagebox.showinfo("碰撞成功！", 
                              f"发现匹配地址！\n地址: {address}\n助记词: {mnemonic}")
        except:
            pass
    
    def _save_result_to_file(self, result: Dict):
        """保存结果到文件"""
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            with open("log.txt", "a", encoding="utf-8") as f:
                f.write(f"\n=== 碰撞成功记录 ===\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"助记词: {result['mnemonic']}\n")
                f.write(f"匹配地址: {result['address']}\n")
                f.write(f"对应私钥: {result['private_key']}\n")
                f.write(f"派生路径: {result['path']}\n")
                f.write(f"工作线程: {result['worker_id']}\n")
                f.write("-" * 50 + "\n")
            
            self.log_callback("结果已保存到 log.txt", "info")
            
        except Exception as e:
            self.log_callback(f"保存结果到文件失败: {e}", "error")
    
    def _update_stats_callback(self):
        """更新统计回调"""
        if self.stats_callback:
            with self.stats_lock:
                stats = {
                    'generation_count': self.generation_count,
                    'collision_count': self.collision_count,
                    'success_count': self.success_count,
                    'current_mnemonic': self.current_mnemonic
                }
            self.stats_callback(stats)
    
    def get_stats(self) -> Dict:
        """获取当前统计信息"""
        with self.stats_lock:
            return {
                'generation_count': self.generation_count,
                'collision_count': self.collision_count,
                'success_count': self.success_count,
                'current_mnemonic': self.current_mnemonic,
                'is_running': self.is_running
            }


class CollisionConfig:
    """碰撞配置类"""
    
    def __init__(self):
        self.mnemonic_type = 12  # 12 or 24
        self.compression_type = "compressed"  # "compressed" or "uncompressed"
        self.derivation_path = "m/44'/0'/0'/0"
        self.thread_count = 4
        self.address_count_per_mnemonic = 20  # 每个助记词生成的地址数量
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'mnemonic_type': self.mnemonic_type,
            'compression_type': self.compression_type,
            'derivation_path': self.derivation_path,
            'thread_count': self.thread_count,
            'address_count_per_mnemonic': self.address_count_per_mnemonic
        }
    
    def from_dict(self, config_dict: Dict):
        """从字典加载配置"""
        self.mnemonic_type = config_dict.get('mnemonic_type', 12)
        self.compression_type = config_dict.get('compression_type', 'compressed')
        self.derivation_path = config_dict.get('derivation_path', "m/44'/0'/0'/0")
        self.thread_count = config_dict.get('thread_count', 4)
        self.address_count_per_mnemonic = config_dict.get('address_count_per_mnemonic', 20)
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        if self.mnemonic_type not in [12, 24]:
            errors.append("助记词类型必须是12或24")
        
        if self.compression_type not in ["compressed", "uncompressed"]:
            errors.append("压缩类型必须是compressed或uncompressed")
        
        if not self.derivation_path.startswith("m/"):
            errors.append("派生路径格式不正确")
        
        if self.thread_count < 1 or self.thread_count > 32:
            errors.append("线程数必须在1-32之间")
        
        if self.address_count_per_mnemonic < 1 or self.address_count_per_mnemonic > 100:
            errors.append("每个助记词的地址数量必须在1-100之间")
        
        return errors


# 测试代码
if __name__ == "__main__":
    # 创建测试目标地址
    test_addresses = {
        "1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa",  # 创世区块地址
        "1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2"   # 另一个测试地址
    }
    
    # 创建配置
    config = CollisionConfig()
    config.thread_count = 2
    
    def test_log_callback(message, level):
        print(f"[{level.upper()}] {message}")
    
    def test_stats_callback(stats):
        print(f"生成: {stats['generation_count']}, 成功: {stats['success_count']}")
    
    # 创建引擎
    engine = CollisionEngine(test_addresses, config.to_dict(), 
                           test_log_callback, test_stats_callback)
    
    try:
        engine.start()
        time.sleep(10)  # 运行10秒
        engine.stop()
    except KeyboardInterrupt:
        engine.stop()
