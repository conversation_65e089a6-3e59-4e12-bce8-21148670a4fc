"""
比特币助记词碰撞器启动脚本
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("正在启动比特币助记词碰撞器...")
    
    try:
        # 检查依赖
        print("检查依赖包...")
        import tkinter
        print("✓ tkinter")
        
        import mnemonic
        print("✓ mnemonic")
        
        import bitcoin
        print("✓ bitcoin")
        
        import pystray
        print("✓ pystray")
        
        import PIL
        print("✓ Pillow")
        
        print("所有依赖包检查完成！")
        print("-" * 40)
        
        # 导入并启动主程序
        print("启动主程序...")
        from main import BitcoinColliderApp
        
        app = BitcoinColliderApp()
        app.run()
        
    except ImportError as e:
        print(f"❌ 依赖包缺失: {e}")
        print("请运行: pip install -r requirements.txt")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
