# 比特币助记词本地碰撞器使用说明

## 程序简介

本程序是一个基于Python和tkinter的比特币助记词碰撞器GUI程序，用于教育和研究目的。程序能够随机生成符合BIP39规范的助记词，并与用户导入的目标地址进行比较，寻找匹配的地址。

## 功能特性

- ✅ 支持BIP39标准的12/24位助记词生成
- ✅ 支持压缩/非压缩公钥选项
- ✅ 可配置HD钱包派生路径
- ✅ 多线程高效碰撞
- ✅ 系统托盘最小化功能
- ✅ 实时统计和日志记录
- ✅ 碰撞成功自动提醒和文件记录

## 安装和启动

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动程序

**方法一：使用启动脚本（推荐）**
```bash
python start.py
```

**方法二：使用批处理文件（Windows）**
双击 `start.bat` 文件

**方法三：直接启动**
```bash
python main.py
```

## 使用方法

### 1. 配置区设置

#### 目标地址文件导入
- 点击"浏览"按钮选择包含目标地址的txt文件
- 文件格式：每行一个比特币地址
- 示例文件：`sample_addresses.txt`

#### 助记词类型
- **12个单词**：标准的12位助记词（推荐）
- **24个单词**：更安全的24位助记词

#### 公钥压缩选项
- **压缩公钥**：生成以1或3开头的地址（推荐）
- **非压缩公钥**：生成传统的以1开头的地址

#### HD钱包派生路径
- 默认：`m/44'/0'/0'/0`
- 可自定义路径，遵循BIP44标准

#### 线程数设置
- 建议设置为CPU核心数
- 范围：1-16个线程

### 2. 操作区

#### 开始碰撞
- 确保已导入目标地址文件
- 点击"开始碰撞"按钮启动
- 程序将持续运行直到手动停止

#### 停止
- 点击"停止"按钮停止碰撞过程

#### 清空结果
- 清空日志区的所有记录
- 重置统计数据

### 3. 运行统计区

实时显示以下信息：
- **运行时间**：程序运行的总时间
- **生成数量**：已生成的助记词总数
- **碰撞数量**：进行比较的总次数
- **成功数量**：找到匹配地址的次数
- **当前助记词**：正在处理的助记词

### 4. 日志区

显示程序运行信息：
- 普通信息（黑色）
- 成功信息（绿色背景）
- 警告信息（橙色）
- 错误信息（红色）

## 系统托盘功能

### 最小化到托盘
- 点击窗口关闭按钮或最小化按钮
- 程序将隐藏到系统托盘

### 托盘菜单
右键点击托盘图标可以：
- **显示程序**：恢复主窗口
- **隐藏程序**：隐藏主窗口
- **关于**：显示程序信息
- **退出程序**：完全退出程序

## 碰撞成功处理

当发现匹配地址时，程序会：

1. **弹窗提醒**：显示匹配信息
2. **日志记录**：在日志区显示详细信息
3. **文件保存**：自动保存到 `log.txt` 文件

### log.txt 文件格式
```
=== 碰撞成功记录 ===
时间: 2024-01-01 12:00:00
助记词: abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about
匹配地址: 1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa
对应私钥: 5HueCGU8rMjxEXxiPuD5BDku4MkFqeZyd4dZ1jvhTVqvbTLvyTJ
派生路径: m/44'/0'/0'/0/0
工作线程: 0
--------------------------------------------------
```

## 注意事项

### ⚠️ 重要警告
- 本程序仅用于教育和研究目的
- 请勿用于非法用途
- 比特币地址碰撞在实际中几乎不可能成功
- 程序运行需要大量计算资源

### 性能优化建议
1. **线程数设置**：设置为CPU核心数可获得最佳性能
2. **目标地址数量**：地址越多，碰撞概率越高
3. **硬件要求**：建议使用多核CPU和充足内存

### 故障排除

#### 程序无法启动
1. 检查Python版本（建议3.7+）
2. 确认所有依赖包已安装
3. 运行 `python start.py` 查看详细错误信息

#### 托盘功能异常
- 某些系统可能不支持托盘功能
- 程序仍可正常使用，只是无法最小化到托盘

#### 内存占用过高
- 减少线程数
- 重启程序释放内存

## 技术说明

### 核心技术
- **BIP39**：助记词生成标准
- **BIP44**：HD钱包派生路径标准
- **ECDSA**：椭圆曲线数字签名算法
- **SHA256/RIPEMD160**：地址生成哈希算法

### 文件结构
```
local_btc/
├── main.py              # 主程序
├── crypto_utils.py      # 加密货币工具模块
├── collision_engine.py  # 碰撞引擎
├── system_tray.py       # 系统托盘功能
├── start.py             # 启动脚本
├── start.bat            # Windows批处理启动
├── requirements.txt     # 依赖包列表
├── sample_addresses.txt # 示例地址文件
├── log.txt              # 碰撞成功记录（自动生成）
└── 使用说明.md          # 本说明文件
```

## 联系和支持

如有问题或建议，请通过以下方式联系：
- 查看程序日志区的错误信息
- 检查 `log.txt` 文件中的详细记录

---

**免责声明**：本程序仅供学习和研究使用，开发者不对任何使用本程序造成的后果承担责任。
