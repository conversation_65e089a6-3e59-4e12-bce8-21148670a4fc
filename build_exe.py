"""
PyInstaller打包脚本
解决依赖包和数据文件的打包问题
"""

import os
import sys
import subprocess

def build_executable():
    """构建可执行文件"""
    
    print("开始构建比特币助记词碰撞器可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # Windows下隐藏控制台
        '--name=BitcoinCollider',       # 可执行文件名
        '--icon=icon.ico',              # 图标文件（如果有的话）
        '--add-data=sample_addresses.txt;.',  # 包含示例文件
        '--hidden-import=mnemonic',     # 确保包含mnemonic模块
        '--hidden-import=hdwallet',     # 确保包含hdwallet模块
        '--hidden-import=ecdsa',        # 确保包含ecdsa模块
        '--hidden-import=base58',       # 确保包含base58模块
        '--hidden-import=pystray',      # 确保包含pystray模块
        '--hidden-import=PIL',          # 确保包含PIL模块
        '--collect-data=mnemonic',      # 收集mnemonic的数据文件
        '--collect-data=hdwallet',      # 收集hdwallet的数据文件
        'bitcoin_collider.py'           # 主程序文件
    ]
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print("可执行文件位置: dist/BitcoinCollider.exe")
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print("错误信息:")
        print(e.stderr)
        return False
    
    except FileNotFoundError:
        print("❌ PyInstaller未安装！")
        print("请运行: pip install pyinstaller")
        return False
    
    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['bitcoin_collider.py'],
    pathex=[],
    binaries=[],
    datas=[('sample_addresses.txt', '.')],
    hiddenimports=[
        'mnemonic',
        'hdwallet',
        'ecdsa',
        'base58',
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集mnemonic的数据文件
from PyInstaller.utils.hooks import collect_data_files
datas = collect_data_files('mnemonic')
a.datas += datas

# 收集hdwallet的数据文件
datas = collect_data_files('hdwallet')
a.datas += datas

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BitcoinCollider',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('bitcoin_collider.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建 bitcoin_collider.spec 文件")

def main():
    """主函数"""
    print("比特币助记词碰撞器打包工具")
    print("=" * 40)
    
    # 检查文件是否存在
    if not os.path.exists('bitcoin_collider.py'):
        print("❌ 找不到 bitcoin_collider.py 文件！")
        return
    
    # 创建spec文件
    create_spec_file()
    
    # 选择打包方式
    print("\n选择打包方式:")
    print("1. 使用命令行参数打包")
    print("2. 使用spec文件打包")
    
    choice = input("请选择 (1 或 2): ").strip()
    
    if choice == "1":
        success = build_executable()
    elif choice == "2":
        try:
            result = subprocess.run(['pyinstaller', 'bitcoin_collider.spec'], 
                                  check=True, capture_output=True, text=True)
            print("✅ 使用spec文件打包成功！")
            success = True
        except Exception as e:
            print(f"❌ 使用spec文件打包失败: {e}")
            success = False
    else:
        print("❌ 无效选择！")
        return
    
    if success:
        print("\n📦 打包完成！")
        print("可执行文件位置: dist/BitcoinCollider.exe")
        print("\n使用说明:")
        print("1. 将 sample_addresses.txt 复制到exe文件同目录")
        print("2. 双击运行 BitcoinCollider.exe")
        print("3. 导入目标地址文件开始碰撞")

if __name__ == "__main__":
    main()
