# 比特币助记词本地碰撞器 - 整合版

## 🎉 整合完成

我已经成功将所有代码整合到一个Python文件中：**`bitcoin_collider.py`**

## 📁 文件说明

### 主要文件
- **`bitcoin_collider.py`** - 主程序文件（包含所有功能，1115行代码）
- **`requirements.txt`** - 依赖包列表
- **`sample_addresses.txt`** - 示例目标地址文件
- **`启动程序.bat`** - Windows启动脚本
- **`使用说明_简化版.txt`** - 简化使用说明

### 辅助文件
- **`使用说明.md`** - 详细使用说明
- **`项目总结.md`** - 项目开发总结
- **`README.md`** - 项目介绍

## 🚀 快速启动

### 方法一：使用批处理文件（推荐）
双击 **`启动程序.bat`** 文件

### 方法二：命令行启动
```bash
python bitcoin_collider.py
```

## ✨ 整合版特点

### 1. 单文件部署
- 所有功能都在一个Python文件中
- 无需多个模块文件
- 便于分发和使用

### 2. 完整功能
- ✅ BIP39助记词生成（12/24位）
- ✅ HD钱包地址派生
- ✅ 多线程碰撞引擎
- ✅ 系统托盘功能
- ✅ 实时统计显示
- ✅ 碰撞成功提醒
- ✅ 自动日志记录

### 3. 用户友好
- 直观的GUI界面
- 详细的使用说明
- 完善的错误处理
- 一键启动脚本

## 📋 使用步骤

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **准备目标地址文件**
   - 创建txt文件，每行一个比特币地址
   - 或使用提供的 `sample_addresses.txt`

3. **启动程序**
   - 双击 `启动程序.bat`
   - 或运行 `python bitcoin_collider.py`

4. **配置参数**
   - 导入目标地址文件
   - 设置助记词类型（12/24位）
   - 选择公钥压缩选项
   - 配置HD钱包路径
   - 设置线程数

5. **开始碰撞**
   - 点击"开始碰撞"按钮
   - 查看实时统计信息
   - 等待碰撞结果

## 🔧 技术架构

### 模块组织
```
bitcoin_collider.py
├── CryptoUtils          # 加密货币工具类
├── CollisionConfig      # 碰撞配置类
├── CollisionEngine      # 碰撞引擎类
├── SystemTray          # 系统托盘类
├── TrayManager         # 托盘管理器
├── BitcoinColliderApp  # 主GUI应用类
└── main()              # 主函数
```

### 核心技术
- **BIP39**: 助记词生成标准
- **BIP44**: HD钱包派生标准
- **ECDSA**: 椭圆曲线数字签名
- **SHA256/RIPEMD160**: 地址生成哈希
- **多线程**: 并发处理提升效率
- **tkinter**: GUI界面框架
- **pystray**: 系统托盘功能

## 📊 性能特点

- **多线程处理**: 支持1-16个工作线程
- **内存优化**: 高效的内存管理
- **线程安全**: 安全的数据同步
- **实时更新**: 1秒间隔统计更新
- **自动保存**: 碰撞成功自动记录

## ⚠️ 重要提醒

1. **仅供学习研究**: 本程序仅用于教育和研究目的
2. **理论概率极低**: 实际碰撞成功概率几乎为零
3. **资源消耗**: 程序运行需要大量CPU资源
4. **合法使用**: 请勿用于任何非法用途

## 🛠️ 故障排除

### 程序无法启动
- 检查Python版本（建议3.7+）
- 确认依赖包已安装
- 查看控制台错误信息

### 托盘功能异常
- 某些系统可能不支持
- 程序仍可正常使用
- 只是无法最小化到托盘

### 内存占用过高
- 减少线程数设置
- 重启程序释放内存

## 📈 项目统计

- **总代码行数**: 1115行
- **开发时间**: 1天
- **文件数量**: 1个主文件
- **功能模块**: 6个核心类
- **依赖包**: 9个Python包

## 🎯 使用建议

1. **线程数设置**: 建议设置为CPU核心数
2. **目标地址**: 地址越多理论概率越高
3. **硬件配置**: 建议多核CPU + 充足内存
4. **运行时间**: 可长时间后台运行

---

**开发完成**: 2024年7月13日  
**版本**: v1.0  
**状态**: 已完成并测试通过  

现在您只需要一个 `bitcoin_collider.py` 文件就可以运行完整的比特币助记词碰撞器了！
